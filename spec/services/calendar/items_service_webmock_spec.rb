# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::ItemsService do
  let(:user_id) { 4 }
  let(:base_url) { 'http://canvas.docker' }
  let(:mock_planner_items) do
    [
      {
        'plannable_id' => '1',
        'plannable_type' => 'assignment',
        'plannable' => {
          'id' => '1',
          'title' => 'Test Assignment',
          'due_at' => '2025-09-05T05:59:59Z'
        },
        'submissions' => false
      }
    ]
  end

  before do
    # Stub the canvas_sync_client to return a client with the test base URL
    allow(Kernel).to receive(:canvas_sync_client).and_return(
      Bearcat::Client.new(prefix: base_url, token: 'dummy_token')
    )
  end

  describe '#call' do
    context 'with basic parameters' do
      let(:service) { described_class.new(user_id) }

      it 'calls the Canvas API with correct path and basic parameters' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner/items")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-09-01T00:00:00.000Z',
              'end_date' => '2025-09-30T00:00:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_items.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_items)
      end
    end

    context 'with date range options' do
      let(:options) do
        {
          start_date: '2025-08-31T18:30:00.000Z',
          end_date: '2025-10-05T18:30:00.000Z'
        }
      end
      let(:service) { described_class.new(user_id, options) }

      it 'includes date parameters in the API call' do
        stub_request(:get, "#{base_url}/api/v1/planner/items")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-08-31T18:30:00.000Z',
              'end_date' => '2025-10-05T18:30:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_items.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_items)
      end
    end

    context 'with context_codes option' do
      let(:options) { { context_codes: %w[course_2 course_1] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes context codes in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner/items")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'context_codes[]' => %w[course_2 course_1]
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_items.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_items)
      end
    end

    context 'with filter option' do
      let(:options) { { filter: 'ungraded_todo_items' } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes filter parameter in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner/items")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'filter' => 'ungraded_todo_items'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_items.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_items)
      end
    end

    context 'with all_ungraded_todo_items filter and user context' do
      let(:options) { { filter: 'all_ungraded_todo_items', context_codes: %w[user_4] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes filter and context parameters in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner/items")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'filter' => 'all_ungraded_todo_items',
              'context_codes[]' => %w[user_4]
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_items.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_items)
      end
    end
  end
end
