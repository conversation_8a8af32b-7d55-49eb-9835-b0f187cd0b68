# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::PlannerNotesService do
  let(:user_id) { 4 }
  let(:base_url) { 'http://canvas.docker' }
  let(:mock_planner_notes) do
    [
      {
        'id' => '1',
        'title' => 'Test Note',
        'description' => 'This is a test planner note',
        'user_id' => user_id,
        'course_id' => nil,
        'todo_date' => '2025-09-05T00:00:00Z',
        'created_at' => '2025-09-04T07:00:00Z',
        'updated_at' => '2025-09-04T07:00:00Z'
      }
    ]
  end

  before do
    # Stub the canvas_sync_client to return a client with the test base URL
    allow(Kernel).to receive(:canvas_sync_client).and_return(
      Bearcat::Client.new(prefix: base_url, token: 'dummy_token')
    )
  end

  describe '#call' do
    context 'with basic parameters' do
      let(:service) { described_class.new(user_id) }

      it 'calls the Canvas API with correct path and basic parameters' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner_notes")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-09-01T00:00:00.000Z',
              'end_date' => '2025-09-30T00:00:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_notes.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_notes)
      end
    end

    context 'with date range options' do
      let(:options) do
        {
          start_date: '2025-08-31T18:30:00.000Z',
          end_date: '2025-10-05T18:30:00.000Z'
        }
      end
      let(:service) { described_class.new(user_id, options) }

      it 'includes date parameters in the API call' do
        stub_request(:get, "#{base_url}/api/v1/planner_notes")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-08-31T18:30:00.000Z',
              'end_date' => '2025-10-05T18:30:00.000Z'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_notes.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_notes)
      end
    end

    context 'with context_codes option' do
      let(:options) { { context_codes: %w[user_4 course_2 course_1] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes context codes in the API call' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        stub_request(:get, "#{base_url}/api/v1/planner_notes")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'context_codes[]' => %w[user_4 course_2 course_1]
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_notes.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_notes)
      end
    end

    context 'with undated parameter' do
      let(:options) { { undated: true } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes undated parameter in the API call' do
        stub_request(:get, "#{base_url}/api/v1/planner_notes")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'undated' => '1'
            )
          )
          .to_return(
            status: 200,
            body: mock_planner_notes.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_notes)
      end
    end

    context 'inherits from BaseService' do
      let(:service) { described_class.new(user_id) }

      it 'is a subclass of Calendar::BaseService' do
        expect(service).to be_a(Calendar::BaseService)
      end

      it 'implements the api_path method' do
        expect(service.send(:api_path)).to eq('api/v1/planner_notes')
      end

      it 'uses only common parameters (no service-specific params)' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 9, 4))

        # PlannerNotesService should only use common parameters from BaseService
        stub_request(:get, "#{base_url}/api/v1/planner_notes")
          .with(
            query: {
              'as_user_id' => user_id.to_s,
              'start_date' => '2025-09-01T00:00:00.000Z',
              'end_date' => '2025-09-30T00:00:00.000Z',
              'context_codes' => ''
            }
          )
          .to_return(
            status: 200,
            body: mock_planner_notes.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_planner_notes)
      end
    end
  end
end
