# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::BaseService do
  let(:user_id) { 123 }
  let(:base_url) { 'https://example.com' }
  let(:mock_response_data) do
    [
      {
        'id' => '1',
        'title' => 'Test Event',
        'start_at' => '2025-01-15T10:00:00Z',
        'end_at' => '2025-01-15T11:00:00Z',
        'context_code' => 'course_1',
        'type' => 'event'
      }
    ]
  end

  # Create a test subclass to test the base functionality
  let(:test_service_class) do
    Class.new(Calendar::BaseService) do
      private

      def api_path
        'api/v1/test_endpoint'
      end

      def build_service_specific_params
        { test_param: 'test_value' }
      end
    end
  end

  before do
    # Stub the canvas_sync_client to return a client with the test base URL
    allow(Kernel).to receive(:canvas_sync_client).and_return(
      Bearcat::Client.new(prefix: base_url, token: 'dummy_token')
    )
  end

  describe '#call' do
    let(:service) { test_service_class.new(user_id) }

    it 'makes HTTP request to the correct endpoint with common parameters' do
      allow(Date).to receive(:today).and_return(Date.new(2025, 1, 15))

      # Use the exact URL format that WebMock expects based on the error message
      stub_request(:get, "#{base_url}/api/v1/test_endpoint")
        .with(
          query: {
            'as_user_id' => user_id.to_s,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
            'context_codes' => '',
            'test_param' => 'test_value'
          }
        )
        .to_return(
          status: 200,
          body: mock_response_data.to_json,
          headers: { 'Content-Type' => 'application/json' }
        )

      result = service.call
      expect(result).to eq(mock_response_data)
    end

    it 'includes custom options in the request' do
      options = {
        start_date: '2025-02-01T00:00:00Z',
        end_date: '2025-02-28T23:59:59Z',
        context_codes: %w[course_1 course_2]
      }
      service = test_service_class.new(user_id, options)

      stub_request(:get, "#{base_url}/api/v1/test_endpoint")
        .with(
          query: hash_including(
            'as_user_id' => user_id.to_s,
            'start_date' => '2025-02-01T00:00:00Z',
            'end_date' => '2025-02-28T23:59:59Z',
            'context_codes[]' => %w[course_1 course_2],
            'test_param' => 'test_value'
          )
        )
        .to_return(
          status: 200,
          body: mock_response_data.to_json,
          headers: { 'Content-Type' => 'application/json' }
        )

      result = service.call
      expect(result).to eq(mock_response_data)
    end
  end

  describe '#api_path' do
    it 'raises NotImplementedError when not overridden' do
      service = Calendar::BaseService.new(user_id)
      expect { service.send(:api_path) }.to raise_error(NotImplementedError)
    end
  end
end
