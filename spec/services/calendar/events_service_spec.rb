# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::EventsService do
  let(:user_id) { 123 }
  let(:mock_client) { double('canvas_sync_client') }
  let(:mock_response) { double('response', all_pages!: []) }

  before do
    allow(Kernel).to receive(:canvas_sync_client).and_return(mock_client)
    allow(mock_client).to receive(:get).and_return(mock_response)
  end

  describe '#call' do
    context 'with basic parameters' do
      let(:service) { described_class.new(user_id) }

      it 'calls the Canvas API with correct path and basic parameters' do
        allow(Date).to receive(:today).and_return(Date.new(2025, 1, 15))

        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            context_codes: nil,
            start_date: '2025-01-01T00:00:00.000Z',
            end_date: '2025-01-31T00:00:00.000Z',
            per_page: 50
          )
        )

        service.call
      end
    end

    context 'with date range options' do
      let(:options) do
        {
          start_date: '2025-01-01T00:00:00Z',
          end_date: '2025-01-31T23:59:59Z'
        }
      end
      let(:service) { described_class.new(user_id, options) }

      it 'includes date parameters in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-01-31T23:59:59Z'
          )
        )

        service.call
      end
    end

    context 'with context_codes option' do
      let(:options) { { context_codes: %w[user_123 course_1 course_2] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes context codes in the API call' do
        stub_request(:get, "#{base_url}/api/v1/calendar_events")
          .with(
            query: hash_including(
              'as_user_id' => user_id.to_s,
              'context_codes[]' => %w[user_123 course_1 course_2]
            )
          )
          .to_return(
            status: 200,
            body: mock_response_data.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        result = service.call
        expect(result).to eq(mock_response_data)
      end
    end

    context 'with include options' do
      let(:options) { { include: ['web_conference', 'series_head'] } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes include parameters in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            include: ['web_conference', 'series_head']
          )
        )

        service.call
      end
    end

    context 'with type filter' do
      let(:options) { { type: 'assignment' } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes type parameter in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            type: 'assignment'
          )
        )

        service.call
      end
    end

    context 'with pagination options' do
      let(:options) { { per_page: 100, page: 2 } }
      let(:service) { described_class.new(user_id, options) }

      it 'includes pagination parameters in the API call' do
        expect(mock_client).to receive(:get).with(
          'api/v1/calendar_events',
          hash_including(
            per_page: 100,
            page: 2
          )
        )

        service.call
      end
    end

    it 'calls all_pages! on the response' do
      service = described_class.new(user_id)
      expect(mock_response).to receive(:all_pages!)

      service.call
    end
  end
end
