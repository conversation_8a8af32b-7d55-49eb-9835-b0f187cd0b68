# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Calendar::EventsService, type: :integration do
  let(:user_id) { 123 }
  let(:base_url) { 'https://example.com' }
  let(:mock_response_data) do
    [
      {
        'id' => '1',
        'title' => 'Test Event',
        'start_at' => '2025-01-15T10:00:00Z',
        'end_at' => '2025-01-15T11:00:00Z',
        'context_code' => 'course_1',
        'type' => 'event'
      }
    ]
  end

  before do
    # Stub the canvas_sync_client to return a client with the test base URL
    allow(Kernel).to receive(:canvas_sync_client).and_return(
      Bearcat::Client.new(prefix: base_url, token: 'dummy_token')
    )
  end

  describe '#call' do
    it 'inherits from BaseService and works correctly' do
      allow(Date).to receive(:today).and_return(Date.new(2025, 1, 15))

      stub_request(:get, "#{base_url}/api/v1/calendar_events")
        .with(
          query: hash_including(
            'as_user_id' => user_id.to_s,
            'start_date' => '2025-01-01T00:00:00.000Z',
            'end_date' => '2025-01-31T00:00:00.000Z'
          )
        )
        .to_return(
          status: 200,
          body: mock_response_data.to_json,
          headers: { 'Content-Type' => 'application/json' }
        )

      service = Calendar::EventsService.new(user_id)
      result = service.call
      
      expect(result).to eq(mock_response_data)
      expect(service).to be_a(Calendar::BaseService)
    end

    it 'includes service-specific parameters' do
      options = {
        include: %w[description location],
        type: 'event',
        all_events: true
      }

      stub_request(:get, "#{base_url}/api/v1/calendar_events")
        .with(
          query: hash_including(
            'as_user_id' => user_id.to_s,
            'include[]' => %w[description location],
            'type' => 'event',
            'all_events' => 'true'
          )
        )
        .to_return(
          status: 200,
          body: mock_response_data.to_json,
          headers: { 'Content-Type' => 'application/json' }
        )

      service = Calendar::EventsService.new(user_id, options)
      result = service.call
      
      expect(result).to eq(mock_response_data)
    end
  end
end
