# frozen_string_literal: true

module Calendar
  class PlannerNotesService
    def initialize(user_id, options = {})
      @user_id = user_id
      @options = options
    end

    def call
      canvas_sync_client.get(api_path, query_params).all_pages!
    end

    private

    attr_reader :user_id, :options

    def api_path
      'api/v1/planner_notes'
    end

    def query_params
      params = {}

      # Call API on behalf of Specific User
      params[:as_user_id] = user_id

      # Date range
      params[:start_date] = options[:start_date] || default_start_date
      params[:end_date] = options[:end_date] || default_end_date

      # Context codes for filtering by course
      params[:context_codes] = options[:context_codes]

      params
    end

    def default_start_date
      Date.today.beginning_of_month.iso8601
    end

    def default_end_date
      Date.today.end_of_month.iso8601
    end
  end
end
