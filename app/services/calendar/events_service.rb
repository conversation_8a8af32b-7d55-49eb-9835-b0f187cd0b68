# frozen_string_literal: true

require_relative 'base_service'

module Calendar
  class EventsService < BaseService
    private

    def api_path
      'api/v1/calendar_events'
    end

    def build_service_specific_params
      params = {}

      # Include parameters
      params[:include] = options[:include] if options[:include].present?

      # Event type filter
      params[:type] = options[:type] if options[:type].in?(%w[assignment sub_assignment])

      params
    end
  end
end
