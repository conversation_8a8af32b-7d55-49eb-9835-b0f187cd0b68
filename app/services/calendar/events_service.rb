# frozen_string_literal: true

module Calendar
  class EventsService
    def initialize(user_id, options = {})
      @user_id = user_id
      @options = options
    end

    def call
      canvas_sync_client.get(api_path, query_params).all_pages!
    end

    private

    attr_reader :user_id, :options

    def api_path
      'api/v1/calendar_events'
    end

    def query_params
      params = build_common_params
      params.merge!(build_service_specific_params)
      params
    end

    def build_common_params
      params = {
        # Call API on behalf of Specific User
        as_user_id: user_id,

        # Context codes for filtering by course
        context_codes: options[:context_codes]
      }

      # Only add date range if not requesting undated items
      unless undated?
        params[:start_date] = options[:start_date] || default_start_date
        params[:end_date] = options[:end_date] || default_end_date
      end

      params
    end

    def build_service_specific_params
      params = {}

      # Include parameters
      params[:include] = options[:include] if options[:include].present?

      # Event type filter
      params[:type] = options[:type] if options[:type].in?(%w[assignment sub_assignment])

      # Additional filters
      params[:undated] = 1 if undated?

      params
    end

    def undated?
      # Check for various truthy representations of undated
      [true, 'true', 1, '1'].include?(options[:undated])
    end

    def default_start_date
      Date.today.beginning_of_month.iso8601
    end

    def default_end_date
      Date.today.end_of_month.iso8601
    end
  end
end
