# frozen_string_literal: true

require_relative 'base_service'

module Calendar
  class EventsService < BaseService
    private

    def api_path
      'api/v1/calendar_events'
    end

    def build_service_specific_params
      params = {}

      # Include parameters
      params[:include] = options[:include] if options[:include].present?

      # Event type filter
      params[:type] = options[:type] if options[:type].present?

      # Additional filters
      params[:all_events] = options[:all_events] if options[:all_events].present?
      params[:undated] = options[:undated] if options[:undated].present?

      params
    end
  end
end
