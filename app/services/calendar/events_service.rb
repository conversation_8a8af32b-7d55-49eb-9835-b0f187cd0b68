# frozen_string_literal: true

module Calendar
  class EventsService
    def initialize(user_id, options = {})
      @user_id = user_id
      @options = options
    end

    def call
      canvas_sync_client.get(api_path, query_params).all_pages!
    end

    private

    attr_reader :user_id, :options

    def api_path
      'api/v1/calendar_events'
    end

    def query_params
      params = {}

      # Call API on behalf of Specific User
      params[:as_user_id] = user_id

      # Context codes - include user and any specified courses
      params[:context_codes] = options[:context_codes]

      # Date range
      params[:start_date] = options[:start_date] || default_start_date
      params[:end_date] = options[:end_date] || default_end_date

      # Include parameters
      params[:include] = options[:include] if options[:include].present?

      # Event type filter
      params[:type] = options[:type] if options[:type].present?

      # Additional filters
      params[:all_events] = options[:all_events] if options[:all_events].present?
      params[:undated] = options[:undated] if options[:undated].present?

      params
    end

    def default_start_date
      Date.today.beginning_of_month.iso8601
    end

    def default_end_date
      Date.today.end_of_month.iso8601
    end
  end
end
