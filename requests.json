{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_connectionId": "946571", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "include%5B%5D", "value": "web_conference"}, {"name": "include%5B%5D", "value": "series_head"}, {"name": "include%5B%5D", "value": "series_natural_language"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1561, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:15 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"1cce01bca06d84250bde909a8b388243\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-07-27T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-07-27T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&start_date=2025-07-27T18%3A30%3A00.000Z&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=8.70, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=0.23, start_processing.action_controller;dur=0.01, sql.active_record;dur=10.74, instance.active_record;dur=0.12, instantiation.active_record;dur=6.82, cache_generate.active_support;dur=2.71, cache_write.active_support;dur=2.16, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.39, process_action.action_controller;dur=64.17"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1531280;m=1531408;u=0.05;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "400.56731851752"}, {"name": "x-request-context-id", "value": "4bdb9f39-6f36-4345-895e-b99eb6d5f3ba"}, {"name": "x-request-cost", "value": "0.060608751004457506"}, {"name": "x-runtime", "value": "0.419465"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2999, "mimeType": "application/json", "compression": -12, "text": "[{\"id\":\"3\",\"title\":\"Student 1 Event 1\",\"start_at\":\"2025-08-12T18:30:00Z\",\"end_at\":\"2025-08-12T18:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-08-20T05:31:26Z\",\"updated_at\":\"2025-08-20T05:31:26Z\",\"all_day\":true,\"all_day_date\":\"2025-08-13\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/3\",\"html_url\":\"http://canvas.docker/calendar?event_id=3\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"4\",\"title\":\"Student 1 Event 2\",\"start_at\":\"2025-08-15T18:30:00Z\",\"end_at\":\"2025-08-15T18:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-08-20T05:31:29Z\",\"updated_at\":\"2025-08-20T05:31:29Z\",\"all_day\":true,\"all_day_date\":\"2025-08-16\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/4\",\"html_url\":\"http://canvas.docker/calendar?event_id=4\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"2\",\"title\":\"21 August 2025 Event\",\"start_at\":\"2025-08-20T18:30:00Z\",\"end_at\":\"2025-08-20T18:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-08-18T12:38:36Z\",\"updated_at\":\"2025-08-18T12:38:52Z\",\"all_day\":true,\"all_day_date\":\"2025-08-21\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/2\",\"html_url\":\"http://canvas.docker/calendar?event_id=2\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false},{\"id\":\"1\",\"title\":\"31 August 2025 Event\",\"start_at\":\"2025-08-30T18:30:00Z\",\"end_at\":\"2025-08-30T18:30:00Z\",\"workflow_state\":\"active\",\"created_at\":\"2025-08-18T12:38:27Z\",\"updated_at\":\"2025-08-18T12:38:46Z\",\"all_day\":true,\"all_day_date\":\"2025-08-31\",\"comments\":null,\"rrule\":\"\",\"series_uuid\":null,\"blackout_date\":false,\"location_address\":null,\"location_name\":\"\",\"type\":\"event\",\"description\":null,\"child_events_count\":0,\"all_context_codes\":\"user_4\",\"context_code\":\"user_4\",\"context_name\":\"Student 1\",\"context_color\":null,\"parent_event_id\":null,\"hidden\":false,\"child_events\":[],\"url\":\"http://canvas.docker/api/v1/calendar_events/1\",\"html_url\":\"http://canvas.docker/calendar?event_id=1\\u0026include_contexts=user_4\",\"duplicates\":[],\"important_dates\":false}]"}, "redirectURL": "", "headersSize": 2715, "bodySize": 3011, "_transferSize": 5726, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 431.3139999867417, "timings": {"blocked": 5.233999997755513, "dns": -1, "ssl": -1, "connect": -1, "send": 0.07200000000000006, "wait": 425.0339999939464, "receive": 0.973999995039776, "_blocked_queueing": 2.4649999977555126, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946608", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&type=assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "type", "value": "assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1484, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:16 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"8e38283aeae6d69b2c86f92c42a2a152\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=assignment&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=19.20, cache_fetch_hit.active_support;dur=0.11, cache_read_multi.active_support;dur=0.49, start_processing.action_controller;dur=0.02, sql.active_record;dur=70.91, instance.active_record;dur=5.18, instantiation.active_record;dur=97.74, cache_generate.active_support;dur=17.95, cache_write.active_support;dur=5.46, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.56, process_action.action_controller;dur=446.48"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1512508;m=1522108;u=0.38;y=0.03;d=0.03;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "2f11d59d-1ef5-4728-ae3a-99c8978b98de"}, {"name": "x-request-cost", "value": "0.4054783309044875"}, {"name": "x-runtime", "value": "0.800968"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 15037, "mimeType": "application/json", "compression": -13, "text": "[{\"title\":\"assignment1\",\"description\":\"\",\"submission_types\":\"online_text_entry,online_upload\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:33:24Z\",\"all_day\":false,\"all_day_date\":\"2025-08-31\",\"id\":\"assignment_3\",\"type\":\"assignment\",\"assignment\":{\"id\":\"3\",\"description\":\"\",\"due_at\":\"2025-08-31T05:59:00Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:33:24Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjNiMTI4ZWE2LTA5ZTAtNDhhZS1hNmUwLTgzNWQ1MGFmODAwZSJ9.ADaw7iJ4ZVO7RX_vEjV6ItUqWVPuQNdglu1We2uFfAs\",\"lti_context_id\":\"3b128ea6-09e0-48ae-a6e0-835d50af800e\",\"course_id\":\"2\",\"name\":\"assignment1\",\"submission_types\":[\"online_text_entry\",\"online_upload\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/3\",\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/2/assignments/3/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/3\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-08-31T05:59:00Z\",\"start_at\":\"2025-08-31T05:59:00Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_3\",\"important_dates\":false},{\"title\":\"GradedQuiz1\",\"submission_types\":\"online_quiz\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:34:10Z\",\"all_day\":false,\"all_day_date\":\"2025-08-31\",\"lock_info\":{\"asset_string\":\"quizzes:quiz_1\",\"context_module\":{\"id\":\"3\",\"name\":\"Quiz\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"description\":null,\"id\":\"assignment_1\",\"type\":\"assignment\",\"assignment\":{\"id\":\"1\",\"due_at\":\"2025-08-31T05:59:59Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":58.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:34:10Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"lock_info\":{\"asset_string\":\"quizzes:quiz_1\",\"context_module\":{\"id\":\"3\",\"name\":\"Quiz\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImM5MmY5YTE5LTcyMjctNGJiZi1hNzA3LWIzZDk1ODYwNDRjMiJ9.68_9V67FphNDqxlbS19_KecX9LKWwuyRTV-SszSHGZQ\",\"lti_context_id\":\"c92f9a19-7227-4bbf-a707-b3d9586044c2\",\"course_id\":\"2\",\"name\":\"GradedQuiz1\",\"submission_types\":[\"online_quiz\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":true,\"can_duplicate\":false,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"description\":null,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/1\",\"quiz_id\":\"1\",\"anonymous_submissions\":false,\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":true,\"lock_explanation\":\"This assignment is part of the module \\u003cb\\u003eQuiz\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_3'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/3/prerequisites/quizzes:quiz_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"submissions_download_url\":\"http://canvas.docker/courses/2/quizzes/1/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/1\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-08-31T05:59:59Z\",\"start_at\":\"2025-08-31T05:59:59Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_1\",\"important_dates\":false},{\"title\":\"discussion1\",\"submission_types\":\"discussion_topic\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:33:44Z\",\"all_day\":false,\"all_day_date\":\"2025-08-31\",\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"description\":null,\"id\":\"assignment_2\",\"type\":\"assignment\",\"assignment\":{\"id\":\"2\",\"due_at\":\"2025-08-31T05:59:59Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:44Z\",\"updated_at\":\"2025-08-18T12:33:44Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":1,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6IjlmZTFlNGUxLTY1MDctNDcxYS05NjZlLTgwZTc5MGE2Y2MzNyJ9.Zn3zZZz4o2GJx1kCMQeur5InjBGnnfpS1P0T21-jY5I\",\"lti_context_id\":\"9fe1e4e1-6507-471a-966e-80e790a6cc37\",\"course_id\":\"2\",\"name\":\"discussion1\",\"submission_types\":[\"discussion_topic\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":true,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"description\":null,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/2\",\"discussion_topic\":{\"id\":\"1\",\"title\":\"discussion1\",\"last_reply_at\":\"2025-08-18T12:32:45Z\",\"created_at\":\"2025-08-18T12:32:44Z\",\"delayed_post_at\":null,\"posted_at\":\"2025-08-18T12:32:45Z\",\"assignment_id\":\"2\",\"root_topic_id\":null,\"position\":null,\"podcast_has_student_posts\":false,\"discussion_type\":\"threaded\",\"lock_at\":null,\"allow_rating\":false,\"only_graders_can_rate\":false,\"sort_by_rating\":false,\"is_section_specific\":false,\"anonymous_state\":null,\"summary_enabled\":false,\"user_name\":\"<EMAIL>\",\"discussion_subentry_count\":0,\"permissions\":{\"attach\":true,\"update\":false,\"reply\":false,\"delete\":false,\"manage_assign_to\":false},\"require_initial_post\":null,\"user_can_see_posts\":true,\"podcast_url\":null,\"read_state\":\"unread\",\"unread_count\":0,\"subscribed\":false,\"attachments\":[],\"published\":true,\"can_unpublish\":false,\"locked\":false,\"can_lock\":true,\"comments_disabled\":false,\"author\":{\"id\":\"1\",\"anonymous_id\":\"1\",\"display_name\":\"<EMAIL>\",\"avatar_image_url\":\"http://canvas.instructure.com/images/messages/avatar-50.png\",\"html_url\":\"http://canvas.docker/courses/2/users/1\",\"pronouns\":null},\"html_url\":\"http://canvas.docker/courses/2/discussion_topics/1\",\"url\":\"http://canvas.docker/courses/2/discussion_topics/1\",\"pinned\":false,\"group_category_id\":null,\"can_group\":true,\"topic_children\":[],\"group_topic_children\":[],\"locked_for_user\":true,\"lock_info\":{\"asset_string\":\"discussion_topic_1\",\"context_module\":{\"id\":\"2\",\"name\":\"Discussion\",\"context_type\":\"Course\",\"context_id\":\"2\",\"workflow_state\":\"active\",\"unlock_at\":null}},\"lock_explanation\":\"This topic is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"message\":\"This topic is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"todo_date\":null,\"is_announcement\":false,\"sort_order\":\"desc\",\"sort_order_locked\":false,\"expanded\":false,\"expanded_locked\":false},\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":true,\"lock_explanation\":\"This assignment is part of the module \\u003cb\\u003eDiscussion\\u003c/b\\u003e and hasn\\u0026#39;t been unlocked yet.\\u003cbr/\\u003e\\u003cdiv class='spinner'\\u003e\\u003c/div\\u003e\\u003ca style='display: none;' class='module_prerequisites_fallback' href='http://canvas.docker/courses/2/modules#module_2'\\u003eVisit the course modules page for information on how to unlock this content.\\u003c/a\\u003e\\u003ca x-canvaslms-trusted-url='/courses/2/modules/2/prerequisites/discussion_topic_1' style='display: none;' id='module_prerequisites_lookup_link'\\u003e\\u0026nbsp;\\u003c/a\\u003e\",\"submissions_download_url\":\"http://canvas.docker/courses/2/assignments/2/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/2\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-08-31T05:59:59Z\",\"start_at\":\"2025-08-31T05:59:59Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_2\",\"important_dates\":false},{\"title\":\"GradedSurvey1\",\"description\":\"\",\"submission_types\":\"online_quiz\",\"workflow_state\":\"published\",\"created_at\":\"2025-08-18T12:32:46Z\",\"updated_at\":\"2025-08-18T12:34:24Z\",\"all_day\":false,\"all_day_date\":\"2025-08-31\",\"id\":\"assignment_4\",\"type\":\"assignment\",\"assignment\":{\"id\":\"4\",\"description\":\"\",\"due_at\":\"2025-08-31T05:59:59Z\",\"unlock_at\":null,\"lock_at\":null,\"points_possible\":10.0,\"grading_type\":\"points\",\"assignment_group_id\":\"1\",\"grading_standard_id\":null,\"created_at\":\"2025-08-18T12:32:46Z\",\"updated_at\":\"2025-08-18T12:34:24Z\",\"peer_reviews\":false,\"automatic_peer_reviews\":false,\"position\":2,\"grade_group_students_individually\":false,\"anonymous_peer_reviews\":false,\"group_category_id\":null,\"post_to_sis\":false,\"moderated_grading\":false,\"omit_from_final_grade\":false,\"intra_group_peer_reviews\":false,\"anonymous_instructor_annotations\":false,\"anonymous_grading\":false,\"graders_anonymous_to_graders\":false,\"grader_count\":0,\"grader_comments_visible_to_graders\":true,\"final_grader_id\":null,\"grader_names_visible_to_final_grader\":true,\"allowed_attempts\":-1,\"annotatable_attachment_id\":null,\"hide_in_gradebook\":false,\"suppress_assignment\":false,\"secure_params\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsdGlfYXNzaWdubWVudF9pZCI6ImIzOTIwZTBiLTE0NjgtNDE2OS1iZmIyLTkzOWE4YzhhNGE3MSJ9.lPEa-JObOt17Gs2CRrKD1KSgGEkc13grBV2hHZFyjnM\",\"lti_context_id\":\"b3920e0b-1468-4169-bfb2-939a8c8a4a71\",\"course_id\":\"2\",\"name\":\"GradedSurvey1\",\"submission_types\":[\"online_quiz\"],\"has_submitted_submissions\":false,\"due_date_required\":false,\"max_name_length\":255,\"in_closed_grading_period\":false,\"graded_submissions_exist\":false,\"user_submitted\":false,\"is_quiz_assignment\":false,\"can_duplicate\":false,\"original_course_id\":null,\"original_assignment_id\":null,\"original_lti_resource_link_id\":null,\"original_assignment_name\":null,\"original_quiz_id\":null,\"workflow_state\":\"published\",\"important_dates\":false,\"muted\":true,\"html_url\":\"http://canvas.docker/courses/2/assignments/4\",\"quiz_id\":\"4\",\"anonymous_submissions\":false,\"published\":true,\"only_visible_to_overrides\":false,\"visible_to_everyone\":true,\"locked_for_user\":false,\"submissions_download_url\":\"http://canvas.docker/courses/2/quizzes/4/submissions?zip=1\",\"post_manually\":false,\"anonymize_students\":false,\"require_lockdown_browser\":false,\"restrict_quantitative_data\":false},\"html_url\":\"http://canvas.docker/courses/2/assignments/4\",\"context_code\":\"course_2\",\"context_name\":\"Course 1\",\"context_color\":null,\"end_at\":\"2025-08-31T05:59:59Z\",\"start_at\":\"2025-08-31T05:59:59Z\",\"url\":\"http://canvas.docker/api/v1/calendar_events/assignment_4\",\"important_dates\":false}]"}, "redirectURL": "", "headersSize": 2478, "bodySize": 15050, "_transferSize": 17528, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 816.1289999843575, "timings": {"blocked": 6.635000004118309, "dns": -1, "ssl": -1, "connect": -1, "send": 0.05600000000000005, "wait": 807.6640000054091, "receive": 1.773999974830076, "_blocked_queueing": 3.3220000041183084, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946616", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&type=sub_assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "type", "value": "sub_assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1488, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:15 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&start_date=2025-07-27T18%3A30%3A00.000Z&type=sub_assignment&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=30.75, cache_fetch_hit.active_support;dur=0.05, cache_read_multi.active_support;dur=0.22, start_processing.action_controller;dur=0.02, sql.active_record;dur=12.84, instance.active_record;dur=0.10, instantiation.active_record;dur=14.34, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.96, process_action.action_controller;dur=100.13"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1510656;m=1512320;u=0.09;y=0.01;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "550.881638525917"}, {"name": "x-request-context-id", "value": "c89ad625-a83b-484b-a7c6-707fddd342af"}, {"name": "x-request-cost", "value": "0.09989204196369561"}, {"name": "x-runtime", "value": "0.465158"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2418, "bodySize": 12, "_transferSize": 2430, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 482.6619999948889, "timings": {"blocked": 7.576000014938414, "dns": -1, "ssl": -1, "connect": -1, "send": 0.06400000000000006, "wait": 474.33999999645164, "receive": 0.6819999834988266, "_blocked_queueing": 4.665000014938414, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946600", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner_notes?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1466, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:15 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=5.15, cache_fetch_hit.active_support;dur=0.03, cache_read_multi.active_support;dur=0.22, start_processing.action_controller;dur=0.02, sql.active_record;dur=2.77, instance.active_record;dur=0.02, instantiation.active_record;dur=0.96, process_action.action_controller;dur=24.93"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner_notes;n=index;b=1512836;m=1512964;u=0.03;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "350.29267021515"}, {"name": "x-request-context-id", "value": "1583e3e0-8715-42f3-839c-e415d50d33aa"}, {"name": "x-request-cost", "value": "0.028429543000025648"}, {"name": "x-runtime", "value": "0.380420"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 1528, "bodySize": 12, "_transferSize": 1540, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 397.9209999961313, "timings": {"blocked": 8.288999998791143, "dns": -1, "ssl": -1, "connect": -1, "send": 0.125, "wait": 388.3310000066757, "receive": 1.1759999906644225, "_blocked_queueing": 5.476999998791143, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946612", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=ungraded_todo_items&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1466, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:15 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"0d073eaf818e050befd6fbe671ce6c5e\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&end_date=2025-08-31T18%3A30%3A00.000Z&filter=ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=14.79, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=1.16, start_processing.action_controller;dur=0.01, sql.active_record;dur=12.46, instance.active_record;dur=0.03, instantiation.active_record;dur=2.76, cache_generate.active_support;dur=40.46, cache_write.active_support;dur=0.22, process_action.action_controller;dur=78.50"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1510540;m=1512396;u=0.04;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "500.704657456156"}, {"name": "x-request-context-id", "value": "792a3c14-5b4c-4f59-988e-97710da17305"}, {"name": "x-request-cost", "value": "0.05046746001689972"}, {"name": "x-runtime", "value": "0.432124"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2345, "bodySize": 12, "_transferSize": 2357, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 455.0210000015795, "timings": {"blocked": 8.497999998968094, "dns": -1, "ssl": -1, "connect": -1, "send": 0.04800000000000004, "wait": 445.62899999269655, "receive": 0.8460000099148601, "_blocked_queueing": 5.541999998968095, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946604", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "2422", "url": "webpack-internal:///./ui/features/calendar/jquery/index.js", "lineNumber": 145, "columnNumber": 36}, {"functionName": "eval", "scriptId": "3161", "url": "webpack-internal:///./ui/features/calendar/jquery/MiniCalendar.js", "lineNumber": 78, "columnNumber": 34}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9298, "columnNumber": 23}, {"functionName": "construct", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 2326, "columnNumber": 12}, {"functionName": "FuncEventSource.fetch", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9297, "columnNumber": 33}, {"functionName": "EventPeriod.requestSource", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8702, "columnNumber": 15}, {"functionName": "EventPeriod.requestSources", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8693, "columnNumber": 17}, {"functionName": "EventManager.setPeriod", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 9042, "columnNumber": 20}, {"functionName": "EventManager.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 8931, "columnNumber": 17}, {"functionName": "Calendar.requestEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 11350, "columnNumber": 33}, {"functionName": "View.fetchInitialEvents", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3907, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 4452, "columnNumber": 16}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5316, "columnNumber": 32}, {"functionName": "onDepChange", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5380, "columnNumber": 24}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5401, "columnNumber": 16}, {"functionName": "intercept", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1625, "columnNumber": 27}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}, {"functionName": "trigger", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8631, "columnNumber": 11}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 8715, "columnNumber": 23}, {"functionName": "EmitterMixin.trigger", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 1647, "columnNumber": 16}, {"functionName": "Model.setProps", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5306, "columnNumber": 21}, {"functionName": "Model.set", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 5246, "columnNumber": 13}, {"functionName": "View.setDate", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 3896, "columnNumber": 17}, {"functionName": "Calendar.render<PERSON>iew", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10927, "columnNumber": 22}, {"functionName": "Calendar.prev", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 10737, "columnNumber": 17}, {"functionName": "buttonClick", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14525, "columnNumber": 52}, {"functionName": "eval", "scriptId": "2426", "url": "webpack-internal:///./node_modules/fullcalendar/dist/fullcalendar.js", "lineNumber": 14553, "columnNumber": 36}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=all_ungraded_todo_items&context_codes%5B%5D=user_4&start_date=2025-07-27T18%3A30%3A00.000Z&end_date=2025-08-31T18%3A30%3A00.000Z&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "YPIvdIAXwLOoAb3kDJ6lESVR/YElA0eKGfgQaY7P2KIZqGYcsm259JhR76hq88JDQRibuV1SLPNJjyNfyr2azw=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "all_ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "start_date", "value": "2025-07-27T18%3A30%3A00.000Z"}, {"name": "end_date", "value": "2025-08-31T18%3A30%3A00.000Z"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1439, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:15 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"11d8274faedd98b488176b0b3598f26d\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-08-31T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-08-31T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&end_date=2025-08-31T18%3A30%3A00.000Z&filter=all_ungraded_todo_items&start_date=2025-07-27T18%3A30%3A00.000Z&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=9.71, cache_fetch_hit.active_support;dur=0.03, cache_read_multi.active_support;dur=0.27, start_processing.action_controller;dur=0.02, sql.active_record;dur=8.77, instance.active_record;dur=0.02, instantiation.active_record;dur=2.72, cache_generate.active_support;dur=28.43, cache_write.active_support;dur=0.49, process_action.action_controller;dur=62.41"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1510280;m=1511048;u=0.05;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "450.65497003946"}, {"name": "x-request-context-id", "value": "37ed9e27-9711-4838-bf38-1358555bf569"}, {"name": "x-request-cost", "value": "0.05467483303600179"}, {"name": "x-runtime", "value": "0.426315"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2257, "bodySize": 12, "_transferSize": 2269, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:15.474Z", "time": 445.59299998218194, "timings": {"blocked": 8.577999980187045, "dns": -1, "ssl": -1, "connect": -1, "send": 0.053999999999999826, "wait": 436.22199999192355, "receive": 0.7390000100713223, "_blocked_queueing": 5.5509999801870435, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946608", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "include%5B%5D", "value": "web_conference"}, {"name": "include%5B%5D", "value": "series_head"}, {"name": "include%5B%5D", "value": "series_natural_language"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1499, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&include%5B%5D=web_conference&include%5B%5D=series_head&include%5B%5D=series_natural_language&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=22.50, cache_fetch_hit.active_support;dur=0.05, cache_read_multi.active_support;dur=1.43, start_processing.action_controller;dur=0.01, sql.active_record;dur=8.04, instance.active_record;dur=0.07, instantiation.active_record;dur=4.30, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.30, process_action.action_controller;dur=48.86"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1522108;m=1522108;u=0.05;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "550.984004131103"}, {"name": "x-request-context-id", "value": "4993aefd-0ee6-4fa3-8a7e-83fd4821d40f"}, {"name": "x-request-cost", "value": "0.05076462598704079"}, {"name": "x-runtime", "value": "0.476073"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2436, "bodySize": 12, "_transferSize": 2448, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 500.29200001154095, "timings": {"blocked": 3.6540000118613243, "dns": -1, "ssl": -1, "connect": -1, "send": 0.15700000000000003, "wait": 495.3369999907445, "receive": 1.1440000089351088, "_blocked_queueing": 1.5610000118613243, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946604", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&type=assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "type", "value": "assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1422, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=assignment&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=31.33, cache_fetch_hit.active_support;dur=0.05, cache_read_multi.active_support;dur=0.20, start_processing.action_controller;dur=0.02, sql.active_record;dur=10.25, instance.active_record;dur=0.09, instantiation.active_record;dur=36.67, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.33, process_action.action_controller;dur=105.05"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1512908;m=1516556;u=0.09;y=0.01;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "600.0"}, {"name": "x-request-context-id", "value": "12940327-2379-460d-b96b-dc78db9a1e4a"}, {"name": "x-request-cost", "value": "0.09827791700987243"}, {"name": "x-runtime", "value": "0.485535"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2191, "bodySize": 12, "_transferSize": 2203, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 503.3040000125766, "timings": {"blocked": 4.039999995965511, "dns": -1, "ssl": -1, "connect": -1, "send": 0.04900000000000038, "wait": 498.34899999992547, "receive": 0.8660000166855752, "_blocked_queueing": 1.6429999959655106, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946616", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&type=sub_assignment&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "type", "value": "sub_assignment"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1426, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/calendar_events?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&type=sub_assignment&undated=1&page=1&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=8.09, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=0.17, start_processing.action_controller;dur=0.01, sql.active_record;dur=9.30, instance.active_record;dur=0.07, instantiation.active_record;dur=3.86, start_transaction.active_record;dur=0.00, transaction.active_record;dur=1.63, process_action.action_controller;dur=50.96"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=calendar_events_api;n=index;t=User;i=4;b=1531408;m=1531408;u=0.04;y=0.00;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "450.46146928856"}, {"name": "x-request-context-id", "value": "73b6d522-50b5-4bbe-ac8b-5ac94b7e494f"}, {"name": "x-request-cost", "value": "0.0438325849895429"}, {"name": "x-runtime", "value": "0.420951"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2216, "bodySize": 12, "_transferSize": 2228, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 435.42699998943135, "timings": {"blocked": 3.717999980600551, "dns": -1, "ssl": -1, "connect": -1, "send": 0.050999999999999934, "wait": 429.7789999911785, "receive": 1.879000017652288, "_blocked_queueing": 1.9199999806005508, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946612", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner_notes?context_codes%5B%5D=user_4&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1404, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"4f53cda18c2baa0c0354bb5f9a3ecbe5\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=4.70, cache_fetch_hit.active_support;dur=0.03, cache_read_multi.active_support;dur=0.18, start_processing.action_controller;dur=0.01, sql.active_record;dur=2.60, instance.active_record;dur=0.02, instantiation.active_record;dur=0.85, process_action.action_controller;dur=14.48"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner_notes;n=index;b=1513408;m=1514048;u=0.01;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "400.14023534523"}, {"name": "x-request-context-id", "value": "cf08051d-53d7-49d8-b98b-b0ef262c5928"}, {"name": "x-request-cost", "value": "0.015606958996793208"}, {"name": "x-runtime", "value": "0.383689"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 1534, "bodySize": 12, "_transferSize": 1546, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 397.94099997379817, "timings": {"blocked": 3.685999975891784, "dns": -1, "ssl": -1, "connect": -1, "send": 0.04499999999999993, "wait": 393.0169999942109, "receive": 1.193000003695488, "_blocked_queueing": 2.163999975891784, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946571", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=ungraded_todo_items&context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "course_2"}, {"name": "context_codes%5B%5D", "value": "course_1"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1404, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"ee59ec3716708865a362cae402e3fa3f\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=course_2&context_codes%5B%5D=course_1&filter=ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=9.00, cache_fetch_hit.active_support;dur=0.04, cache_read_multi.active_support;dur=0.17, start_processing.action_controller;dur=0.01, sql.active_record;dur=9.05, instance.active_record;dur=0.03, instantiation.active_record;dur=1.59, cache_generate.active_support;dur=30.21, cache_write.active_support;dur=0.21, process_action.action_controller;dur=53.32"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1512648;m=1512648;u=0.03;y=0.01;d=0.01;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "550.402632889017"}, {"name": "x-request-context-id", "value": "e59e2fc9-4cf6-4ff0-ab0f-acc5961aef14"}, {"name": "x-request-cost", "value": "0.03933949898513128"}, {"name": "x-runtime", "value": "0.422849"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2131, "bodySize": 12, "_transferSize": 2143, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 436.3270000030752, "timings": {"blocked": 3.9109999885894355, "dns": -1, "ssl": -1, "connect": -1, "send": 0.04600000000000004, "wait": 431.5630000104979, "receive": 0.8070000039879233, "_blocked_queueing": 2.222999988589436, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "946600", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "J.<PERSON>ttpRequest.send", "scriptId": "10", "url": "chrome-extension://mdnleldcmiljblolnjhpnblkcekpdkpa/page-scripts/ajaxRequestInterceptor.ps.js", "lineNumber": 0, "columnNumber": 12652}], "parent": {"description": "await", "callFrames": [{"functionName": "send", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9942, "columnNumber": 9}, {"functionName": "ajax", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 9523, "columnNumber": 14}, {"functionName": "eval", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 379, "columnNumber": 21}, {"functionName": "obj.<computed>", "scriptId": "47", "url": "webpack-internal:///./node_modules/jquery-migrate/dist/jquery-migrate.js", "lineNumber": 181, "columnNumber": 19}, {"functionName": "eval", "scriptId": "1224", "url": "webpack-internal:///./ui/shared/jquery/jquery.ajaxJSON.js", "lineNumber": 114, "columnNumber": 60}, {"functionName": "fetchNextBatch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 854, "columnNumber": 69}, {"functionName": "eval", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 817, "columnNumber": 37}, {"functionName": "startFetch", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 820, "columnNumber": 21}, {"functionName": "getEvents", "scriptId": "3244", "url": "webpack-internal:///./ui/shared/calendar/jquery/EventDataSource.js", "lineNumber": 696, "columnNumber": 28}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 64, "columnNumber": 32}, {"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 118, "columnNumber": 18}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "eval", "scriptId": "3245", "url": "webpack-internal:///./ui/features/calendar/jquery/UndatedEventsList.js", "lineNumber": 116, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 5147, "columnNumber": 26}, {"functionName": "elemData.handle", "scriptId": "46", "url": "webpack-internal:///./packages/jquery/jquery.js", "lineNumber": 4951, "columnNumber": 27}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "80", "request": {"method": "GET", "url": "http://canvas.docker/api/v1/planner/items?filter=all_ungraded_todo_items&context_codes%5B%5D=user_4&undated=1&per_page=50", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/javascript, application/json+canvas-string-ids, */*; q=0.01"}, {"name": "Accept-Encoding", "value": "gzip, deflate"}, {"name": "Accept-Language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "canvas.docker"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://canvas.docker/calendar"}, {"name": "Sec-GPC", "value": "1"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-CSRF-Token", "value": "tqsS0FjRVoHBbrR4eNFE07K+J9prUPQ5R/76Lf+uyOHP8Vu4aqsvxvE+5jQevCOB1vdB4hMBn0AXickbu9yKjA=="}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}], "queryString": [{"name": "filter", "value": "all_ungraded_todo_items"}, {"name": "context_codes%5B%5D", "value": "user_4"}, {"name": "undated", "value": "1"}, {"name": "per_page", "value": "50"}], "cookies": [], "headersSize": 1377, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Tue, 02 Sep 2025 18:14:18 GMT"}, {"name": "Server", "value": "nginx/1.21.6"}, {"name": "Status", "value": "200 OK"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "cache-control", "value": "max-age=0, private, must-revalidate"}, {"name": "content-security-policy", "value": "frame-ancestors 'self' canvas.docker staging.canvas.docker beta.canvas.docker test.canvas.docker qa.canvas.docker;"}, {"name": "etag", "value": "W/\"6a3a1a86a08d2fd18f0a3bcad1156f0c\""}, {"name": "link", "value": "<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"current\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"first\",<http://canvas.docker/api/v1/planner/items?context_codes%5B%5D=user_4&filter=all_ungraded_todo_items&undated=1&page=first&per_page=50>; rel=\"last\""}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server-timing", "value": "cache_read.active_support;dur=5.21, cache_fetch_hit.active_support;dur=0.03, cache_read_multi.active_support;dur=0.21, start_processing.action_controller;dur=0.02, sql.active_record;dur=6.46, instance.active_record;dur=0.02, instantiation.active_record;dur=1.27, cache_generate.active_support;dur=19.45, cache_write.active_support;dur=0.26, process_action.action_controller;dur=41.29"}, {"name": "x-canvas-meta", "value": "a=2;g=aRscIEBuVS2OBUfwSYYZJdTpJJ956yzu9i3PnxVN;s=1;c=development;o=planner;n=index;b=1514180;m=1515908;u=0.04;y=0.00;d=0.00;"}, {"name": "x-canvas-user-id", "value": "10000000000004"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-rate-limit-remaining", "value": "500.42360938904"}, {"name": "x-request-context-id", "value": "c7f23db9-7582-47f2-96b8-a6643944bb0a"}, {"name": "x-request-cost", "value": "0.042947752001774475"}, {"name": "x-runtime", "value": "0.421702"}, {"name": "x-session-id", "value": "ed689f9ffe4061e5de3ae4301cf31943"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2, "mimeType": "application/json", "compression": -10, "text": "[]"}, "redirectURL": "", "headersSize": 2062, "bodySize": 12, "_transferSize": 2074, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "*************", "startedDateTime": "2025-09-02T18:14:17.665Z", "time": 435.4379999858793, "timings": {"blocked": 3.9319999923184517, "dns": -1, "ssl": -1, "connect": -1, "send": 0.050000000000000044, "wait": 429.4369999867212, "receive": 2.019000006839633, "_blocked_queueing": 2.2769999923184514, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}